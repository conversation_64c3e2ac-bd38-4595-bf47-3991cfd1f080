# Images generator

Script generates files by format and resolution.

## Prerequisites

Create `.env` file in `server` folder with FTP credentials.

Create `.env` file in `client` folder.

## Production

### Start server

Start Node server:

```shell
cd server
pnpm run start:server
```

Server will be accessible on http://localhost:PORT, e.g.: http://localhost:3000.

### Start client

```shell
cd client
pnpm run start
```

Client will be accessible on http://localhost:VITE_PORT, e.g.: http://localhost:3001.

### Start fake FTP server

For development purpouses can be started fake FTP server to imitate images uploads:

```shell
cd server
pnpm run start:ftp-server
```

## Development

Start Node server:

```shell
cd server
pnpm run start:server:dev
```

Start client:

```shell
cd client
pnpm run dev
```

Start fake FTP server (optional) (TODO):

```shell
cd server
pnpm run start:ftp-server:dev
```
