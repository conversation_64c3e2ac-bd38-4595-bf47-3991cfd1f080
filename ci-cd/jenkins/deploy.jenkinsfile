#!/usr/bin/env groovy

@Library('shared-libs') _

node('master') {
    def CHANGES = ""
    def NAME = "images-optimization"
    def SLACK_URL = "*******************************************************************************"
    try {
        stage ("Checkout Code"){
             checkout scm
          }
        stage('Deploy') {
                sh "cap ${params.target} ${params.action}"
        }
        stage('Notify') {
            slack_success("${NAME}","${SLACK_URL}","${env.JOB_NAME}","${env.BUILD_NUMBER}","${CHANGES}","${env.BUILD_ID}","${env.JOB_URL}")
        }
    } catch(error) {
        currentBuild.result = "FAILED"
        slack_error ("${NAME}","${SLACK_URL}","${env.JOB_NAME}","${env.BUILD_NUMBER}","${env.GIT_AUTHOR_NAME}","${error}","${env.BUILD_ID}","${env.JOB_URL}")
    } finally {
        stage('Clean up') {
            deleteDir()
        }
    }
}