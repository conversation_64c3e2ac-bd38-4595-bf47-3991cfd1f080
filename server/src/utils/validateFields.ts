import path from 'path';
import { Request } from 'express';
import { simplifyFileName } from './simplifyFileName';
import {
    AVAILABLE_FORMATS,
    AVAILABLE_RESOLUTIONS,
    DEFAULT_BRANDED,
    DEFAULT_QUALITIES,
} from '../constants';
import { IFormat, IFormats, IQualities } from '../interfaces';

interface IValidateFields {
    imagesPath: string;
    fileNames: string[];
    branded: string;
    resolutions: number[];
    formats: IFormats;
    qualities: IQualities;
}

export const validateFields = (req: Request): IValidateFields => {
    const imagesPath = path.join(req.body.imagesPath?.trim()?.replace(/\.\.\/?/g, ''));
    const fileNames = Array.isArray(req.body.fileNames) ? req.body.fileNames : [];
    const branded = simplifyFileName(req.body.branded) || DEFAULT_BRANDED;
    const resolutions = ((req.body?.resolutions || []) as string[])
        .map(Number)
        .filter((resolution) => AVAILABLE_RESOLUTIONS.includes(Number(resolution)));

    const formats = ((req.body?.formats || []) as IFormats).filter((format) =>
        AVAILABLE_FORMATS.includes(format),
    );

    const qualities = DEFAULT_QUALITIES;

    if (req.body?.qualities) {
        (Object.entries(req.body?.qualities) as [IFormat, number][]).forEach(
            ([format, quality]) => {
                if (
                    AVAILABLE_FORMATS.includes(format) &&
                    !isNaN(quality) &&
                    quality > 0 &&
                    quality <= 100
                ) {
                    qualities[format] = Number(quality);
                }
            },
        );
    }

    return {
        imagesPath,
        fileNames,
        branded,
        resolutions,
        formats,
        qualities,
    };
};

export const validateImages = (req: Request): string[] => {
    if (!Array.isArray(req.body?.images)) {
        return [];
    }

    return req.body.images;
};
