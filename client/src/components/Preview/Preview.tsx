import { useForm } from '../../state/useForm';

export const Preview = () => {
    const images = useForm((state) => state.form.images);

    return (
        <>
            <div className="fs-5">Images preview</div>
            <div className="align-items-center d-flex flex-column gap-3">
                {images.map((imageFile, index) => (
                    <img
                        key={index}
                        src={URL.createObjectURL(imageFile)}
                        alt=""
                        className="img-fluid"
                    />
                ))}
            </div>
        </>
    );
};
