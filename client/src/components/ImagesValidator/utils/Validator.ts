import { GameId, ImageFormat, ImageResolution } from '../../../enums';
import { DEFAULT_BRANDED_PATH, STATIC_SERVER_IMAGE_URL } from '../../../constants';
import { GAMES_NAMES, IMAGE_CONFIG } from './config';

class Validator {
    private running = false;

    private buildUrl(
        path: string,
        branded: string,
        filename: string,
        size: ImageResolution,
        format: ImageFormat,
    ): string {
        return `${STATIC_SERVER_IMAGE_URL}/${path}/${branded}/${filename}-${size}.${format}`;
    }

    private async loadImage(url: string): Promise<boolean> {
        return await new Promise((resolve) => {
            const image = new Image();
            image.src = url;
            image.onload = () => {
                console.log('Image loaded:', image.src);
                resolve(true);
            };
            image.onerror = () => {
                console.error('Image failed to load:', image.src);
                resolve(false);
            };
        });
    }

    public createList(): string[] {
        return Object.values(IMAGE_CONFIG).reduce<string[]>((acc, config) => {
            for (const filename of config.filenames) {
                for (const size of config.sizes) {
                    for (const format of config.formats) {
                        acc.push(
                            this.buildUrl(
                                config.path,
                                DEFAULT_BRANDED_PATH,
                                filename,
                                size,
                                format,
                            ),
                        );
                    }
                }
            }

            if (config.branded) {
                for (const [gameId, brandedList] of Object.entries(config.branded)) {
                    for (const branded of brandedList) {
                        for (const size of config.sizes) {
                            for (const format of config.formats) {
                                acc.push(
                                    this.buildUrl(
                                        config.path,
                                        branded,
                                        GAMES_NAMES[Number(gameId) as GameId],
                                        size,
                                        format,
                                    ),
                                );
                            }
                        }
                    }
                }
            }

            return acc;
        }, []);
    }

    public async start(
        list: string[],
        callback: (url: string, status: boolean) => void,
    ): Promise<void> {
        this.running = true;

        for (let i = 0; i < list.length; i++) {
            if (!this.running) {
                return;
            }

            const url = list[i];

            await this.loadImage(url).then((status) => {
                callback(url, status);
            });
        }

        this.running = false;
    }

    public stop(): void {
        this.running = false;
    }
}

export const validator = new Validator();
